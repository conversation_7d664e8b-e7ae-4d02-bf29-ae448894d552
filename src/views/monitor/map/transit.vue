<template>
  <div class="transit-map-page">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">公交调度地图</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>实时监控</el-breadcrumb-item>
          <el-breadcrumb-item>地图监控</el-breadcrumb-item>
          <el-breadcrumb-item>公交调度地图</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-space>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </el-space>
      </div>
    </div>

    <div class="fullscreen-map">
      <el-card shadow="hover">
        <div class="map-container">
          <div id="transit-amap-container" class="amap-wrapper"></div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="TransitMapView" lang="ts">
import { ArrowLeft } from '@element-plus/icons-vue'
import {
  createOptimizedMap,
  addOptimizedControls,
  createOptimizedMarker,
  createOptimizedPolyline,
  suppressMapWarnings,
  handleMapError
} from '@/utils/mapConfig'

const router = useRouter()
const mapInstance = ref<any>(null)
const mapLoaded = ref(false)

const goBack = () => {
  router.back()
}

// 初始化公交调度地图
const initTransitMap = () => {
  try {
    // 抑制警告
    suppressMapWarnings()

    // 创建优化的地图实例
    const map = createOptimizedMap('transit-amap-container', { zoom: 12 })

    // 添加优化的控件
    addOptimizedControls(map)

    // 添加公交站点标记
    const stations = [
      { position: [116.397428, 39.90923] as [number, number], title: '天安门站' },
      { position: [116.407428, 39.90923] as [number, number], title: '王府井站' },
      { position: [116.417428, 39.91423] as [number, number], title: '东单站' }
    ]

    stations.forEach(station => {
      const marker = createOptimizedMarker(station.position, station.title)
      map.add(marker)
    })

    // 添加优化的公交路线
    const routePath: [number, number][] = [
      [116.397428, 39.90923],
      [116.407428, 39.90923],
      [116.417428, 39.91423]
    ]
    const polyline = createOptimizedPolyline(routePath, '#1890ff')
    map.add(polyline)

    // 地图加载完成事件
    map.on('complete', () => {
      console.log('公交调度地图加载完成')
      mapLoaded.value = true
      ElMessage.success('公交调度地图加载成功')
    })

    mapInstance.value = map
  } catch (error) {
    handleMapError(error, '公交调度地图初始化')
  }
}

// 生命周期
onMounted(() => {
  // 确保高德地图API已加载
  if (typeof AMap !== 'undefined') {
    initTransitMap()
  } else {
    // 等待API加载
    const checkAMap = setInterval(() => {
      if (typeof AMap !== 'undefined') {
        clearInterval(checkAMap)
        initTransitMap()
      }
    }, 100)

    // 10秒后超时
    setTimeout(() => {
      clearInterval(checkAMap)
      if (!mapLoaded.value) {
        ElMessage.error('地图API加载超时')
      }
    }, 10000)
  }
})

onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.destroy()
  }
})
</script>

<style scoped>
.transit-map-page {
  padding: 16px;
  height: 100vh;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.fullscreen-map {
  height: calc(100vh - 120px);
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.amap-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
</style>
