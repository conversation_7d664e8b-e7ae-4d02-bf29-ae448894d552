import { createWebHistory, createRouter, RouteRecordRaw } from 'vue-router';
/* Layout */
import Layout from '@/layout/index.vue';
import TopMenuLayout from '@/layout/TopMenuLayout.vue';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: TopMenuLayout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/social-callback',
    hidden: true,
    component: () => import('@/layout/components/SocialCallback/index.vue')
  },
  {
    path: '/login',
    component: () => import('@/views/login.vue'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register.vue'),
    hidden: true
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404.vue'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    hidden: true
  },
  {
    path: '',
    component: TopMenuLayout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: TopMenuLayout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  // 基本信息维护
  {
    path: '/basic',
    component: TopMenuLayout,
    redirect: '/basic/driver',
    name: 'Basic',
    meta: { title: '基本信息维护', icon: 'system' },
    children: [
      {
        path: 'driver',
        component: () => import('@/views/basic/driver/index.vue'),
        name: 'Driver',
        meta: { title: '司机管理', icon: 'user' }
      },
      {
        path: 'vehicle',
        component: () => import('@/views/basic/vehicle/index.vue'),
        name: 'Vehicle',
        meta: { title: '车辆档案管理', icon: 'guide' }
      },
      {
        path: 'vehicle/fuel',
        component: () => import('@/views/basic/vehicle/fuel.vue'),
        name: 'VehicleFuel',
        meta: { title: '车辆加油记录', icon: 'edit' },
        hidden: true
      },
      {
        path: 'vehicle/driving',
        component: () => import('@/views/basic/vehicle/driving.vue'),
        name: 'VehicleDriving',
        meta: { title: '车辆行驶记录', icon: 'edit' },
        hidden: true
      },
      {
        path: 'vehicle/maintenance',
        component: () => import('@/views/basic/vehicle/maintenance.vue'),
        name: 'VehicleMaintenance',
        meta: { title: '维修保养记录', icon: 'edit' },
        hidden: true
      },
      {
        path: 'route',
        component: () => import('@/views/basic/route/index.vue'),
        name: 'Route',
        meta: { title: '线路基础信息', icon: 'tree' }
      },
      {
        path: 'station',
        component: () => import('@/views/basic/station/index.vue'),
        name: 'Station',
        meta: { title: '站点信息维护', icon: 'tree-table' }
      },
      {
        path: 'operation',
        component: () => import('@/views/basic/operation/index.vue'),
        name: 'Operation',
        meta: { title: '运营参数设置', icon: 'tool' }
      }
    ]
  },
  // 排班调度
  {
    path: '/schedule',
    component: TopMenuLayout,
    redirect: '/schedule/template',
    name: 'Schedule',
    meta: { title: '排班调度', icon: 'date' },
    children: [
      {
        path: 'template',
        component: () => import('@/views/schedule/template/index.vue'),
        name: 'Template',
        meta: { title: '基础模板配置', icon: 'form' }
      },
      {
        path: 'template/fixed',
        component: () => import('@/views/schedule/template/fixed.vue'),
        name: 'FixedTemplate',
        meta: { title: '固定间隔发车设置', icon: 'edit' },
        hidden: true
      },
      {
        path: 'template/peak',
        component: () => import('@/views/schedule/template/peak.vue'),
        name: 'PeakTemplate',
        meta: { title: '高峰加密发车设置', icon: 'edit' },
        hidden: true
      },
      {
        path: 'template/custom',
        component: () => import('@/views/schedule/template/custom.vue'),
        name: 'CustomTemplate',
        meta: { title: '自定义排班编辑', icon: 'edit' },
        hidden: true
      },
      {
        path: 'resource',
        component: () => import('@/views/schedule/resource/index.vue'),
        name: 'Resource',
        meta: { title: '资源分配', icon: 'peoples' }
      },
      {
        path: 'resource/vehicle',
        component: () => import('@/views/schedule/resource/vehicle.vue'),
        name: 'VehicleAllocation',
        meta: { title: '车辆分配管理', icon: 'edit' },
        hidden: true
      },
      {
        path: 'resource/driver',
        component: () => import('@/views/schedule/resource/driver.vue'),
        name: 'DriverAllocation',
        meta: { title: '司机分配管理', icon: 'edit' },
        hidden: true
      },
      {
        path: 'plan',
        component: () => import('@/views/schedule/plan/index.vue'),
        name: 'Plan',
        meta: { title: '计划管理', icon: 'list' }
      },
      {
        path: 'plan/create',
        component: () => import('@/views/schedule/plan/create.vue'),
        name: 'PlanCreate',
        meta: { title: '排班计划制定', icon: 'edit' },
        hidden: true
      },
      {
        path: 'demo',
        component: () => import('@/views/demo/schedule-demo.vue'),
        name: 'ScheduleDemo',
        meta: { title: '拖拽排班演示', icon: 'magic-stick' },
        hidden: false
      },
      {
        path: 'plan/adjust',
        component: () => import('@/views/schedule/plan/adjust.vue'),
        name: 'PlanAdjust',
        meta: { title: '临时调整处理', icon: 'edit' },
        hidden: true
      }
    ]
  },
  // 实时监控
  {
    path: '/transit-monitor',
    component: TopMenuLayout,
    redirect: '/transit-monitor/vehicle',
    name: 'TransitMonitor',
    meta: { title: '实时监控', icon: 'monitor' },
    children: [
      {
        path: 'vehicle',
        component: () => import('@/views/monitor/vehicle/index.vue'),
        name: 'VehicleMonitor',
        meta: { title: '车辆监控', icon: 'online' }
      },
      {
        path: 'vehicle/location',
        component: () => import('@/views/monitor/vehicle/location.vue'),
        name: 'VehicleLocation',
        meta: { title: '实时位置跟踪', icon: 'edit' },
        hidden: true
      },
      {
        path: 'vehicle/status',
        component: () => import('@/views/monitor/vehicle/status.vue'),
        name: 'VehicleStatus',
        meta: { title: '运行状态监控', icon: 'edit' },
        hidden: true
      },
      {
        path: 'vehicle/alert',
        component: () => import('@/views/monitor/vehicle/alert.vue'),
        name: 'VehicleAlert',
        meta: { title: '异常情况告警', icon: 'edit' },
        hidden: true
      },
      {
        path: 'dispatch',
        component: () => import('@/views/monitor/dispatch/index.vue'),
        name: 'Dispatch',
        meta: { title: '调度指令', icon: 'phone' }
      },
      {
        path: 'dispatch/voice',
        component: () => import('@/views/monitor/dispatch/voice.vue'),
        name: 'DispatchVoice',
        meta: { title: '语音功能维护', icon: 'edit' },
        hidden: true
      },
      {
        path: 'dispatch/emergency',
        component: () => import('@/views/monitor/dispatch/emergency.vue'),
        name: 'DispatchEmergency',
        meta: { title: '紧急调度', icon: 'edit' },
        hidden: true
      },
      {
        path: 'display',
        component: () => import('@/views/monitor/display/index.vue'),
        name: 'Display',
        meta: { title: '数据展示', icon: 'chart' }
      },
      {
        path: 'display/screen',
        component: () => import('@/views/monitor/display/screen.vue'),
        name: 'DisplayScreen',
        meta: { title: '大屏展示', icon: 'edit' },
        hidden: true
      },
      {
        path: 'display/statistics',
        component: () => import('@/views/monitor/display/statistics.vue'),
        name: 'DisplayStatistics',
        meta: { title: '统计报表', icon: 'edit' },
        hidden: true
      },
      {
        path: 'display/passenger',
        component: () => import('@/views/monitor/display/passenger.vue'),
        name: 'DisplayPassenger',
        meta: { title: '客流量统计', icon: 'edit' },
        hidden: true
      },
      {
        path: 'display/realtime',
        component: () => import('@/views/monitor/display/realtime.vue'),
        name: 'DisplayRealtime',
        meta: { title: '实时数据展示', icon: 'edit' },
        hidden: true
      },
      {
        path: 'map',
        component: () => import('@/views/monitor/map/index.vue'),
        name: 'MonitorMap',
        meta: { title: '地图监控', icon: 'location' }
      },
      {
        path: 'map/transit',
        component: () => import('@/views/monitor/map/transit.vue'),
        name: 'TransitMapView',
        meta: { title: '公交调度地图', icon: 'edit' },
        hidden: true
      }
    ]
  },
  // 日志查看
  {
    path: '/logs',
    component: TopMenuLayout,
    redirect: '/logs/operation',
    name: 'Logs',
    meta: { title: '日志查看', icon: 'log' },
    children: [
      {
        path: 'operation',
        component: () => import('@/views/logs/operation/index.vue'),
        name: 'OperationLog',
        meta: { title: '操作日志', icon: 'documentation' }
      },
      {
        path: 'dispatcher',
        component: () => import('@/views/logs/dispatcher/index.vue'),
        name: 'DispatcherLog',
        meta: { title: '调度员日志', icon: 'user' }
      }
    ]
  },
  // 开发测试模块暂时注释，等待创建demo页面
  // {
  //   path: '/dev',
  //   component: TopMenuLayout,
  //   redirect: '/dev/map-demo',
  //   name: 'Dev',
  //   meta: { title: '开发测试', icon: 'bug' },
  //   hidden: import.meta.env.PROD,
  //   children: [
  //     {
  //       path: 'map-demo',
  //       component: () => import('@/views/demo/map/index.vue'),
  //       name: 'MapDemo',
  //       meta: { title: '地图功能演示', icon: 'location' }
  //     },
  //     {
  //       path: 'map-test',
  //       component: () => import('@/views/demo/map-test/index.vue'),
  //       name: 'MapTest',
  //       meta: { title: '地图功能测试', icon: 'monitor' }
  //     }
  //   ]
  // }
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes: RouteRecordRaw[] = [

];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_CONTEXT_PATH),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }
    return { top: 0 };
  }
});

export default router;
